<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $post->title }}
        </h2>
    </x-slot>

    <div class="py-12 bg-gradient-to-br from-slate-100 via-purple-50 to-violet-100 min-h-screen">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-6">
                    <div class="mb-6">
                        <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $post->title }}</h1>
                        <!-- معلومات المؤلف والتاريخ -->
                        <div class="mb-6 pb-4 border-b border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                        {{ substr($post->user->name ?? 'U', 0, 1) }}
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-800">Created by {{ $post->user->name ?? 'Unknown Author' }}</p>
                                        <p class="text-sm text-gray-500">
                                            📅 {{ $post->created_at->format('F j, Y \a\t g:i A') }}
                                        </p>
                                        <p class="text-xs text-gray-400">
                                            🕒 {{ $post->created_at->diffForHumans() }}
                                        </p>
                                    </div>
                                </div>
                                <div class="text-sm text-gray-500">
                                    {{ str_word_count($post->description) }} words • {{ ceil(str_word_count($post->description) / 200) }} min read
                                </div>
                            </div>
                        </div>

                        <div class="prose max-w-none">
                            <p class="text-gray-700 leading-relaxed whitespace-pre-line text-lg">{{ $post->description }}</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 pt-6 border-t border-gray-200">
                        <a href="{{ route('posts.index') }}"
                           class="bg-gray-300 hover:bg-gray-400 text-black font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                            Back to Posts
                        </a>

                        <a href="{{ route('posts.edit', $post) }}"
                           class="bg-blue-600 hover:bg-blue-700 text-black font-bold py-2 px-4 rounded-lg transition-colors duration-200">
                            Edit Post
                        </a>

                        <form action="{{ route('posts.destroy', $post) }}" method="POST" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit"
                                    class="bg-red-600 hover:bg-red-700 text-black font-bold py-2 px-4 rounded-lg transition-colors duration-200"
                                    onclick="return confirm('Are you sure you want to delete this post?')">
                                Delete Post
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
